'use client'

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import {
  NPISection,
  NPISectionHeader,
  NPISectionTitle,
  NPISectionDescription,
} from '@/components/ui/npi-section'
import { N<PERSON><PERSON>ard, NPICardHeader, NPICardTitle, NPICardContent } from '@/components/ui/npi-card'
import { NPIButton } from '@/components/ui/npi-button'
import { Mail, Phone, MapPin, Clock, Send, User, MessageSquare } from 'lucide-react'

interface ContactInfo {
  icon: React.ReactNode
  title: string
  details: string[]
}

interface NPIContactFormProps {
  title?: string
  description?: string
  contactInfo?: ContactInfo[]
}

export const NPIContactFormBlock: React.FC<NPIContactFormProps> = ({
  title = 'Contact Us',
  description = "Get in touch with the NPI team. We're here to answer your questions, provide information, and explore collaboration opportunities.",
  contactInfo = [
    {
      icon: <MapPin className="w-6 h-6" />,
      title: 'Office Address',
      details: [
        'National Museums of Kenya',
        'Museum Hill Road',
        'P.O. Box 40658-00100',
        'Nairobi, Kenya',
      ],
    },
    {
      icon: <Phone className="w-6 h-6" />,
      title: 'Phone & Email',
      details: ['+254 20 374 2131', '+254 20 374 2161', '<EMAIL>', '<EMAIL>'],
    },
    {
      icon: <Clock className="w-6 h-6" />,
      title: 'Office Hours',
      details: [
        'Monday - Friday',
        '8:00 AM - 5:00 PM',
        'Saturday: 9:00 AM - 1:00 PM',
        'Sunday: Closed',
      ],
    },
  ],
}) => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    organization: '',
    subject: '',
    category: '',
    message: '',
  })

  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>,
  ) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    // Simulate form submission
    await new Promise((resolve) => setTimeout(resolve, 2000))

    // Reset form
    setFormData({
      name: '',
      email: '',
      organization: '',
      subject: '',
      category: '',
      message: '',
    })
    setIsSubmitting(false)
    alert('Thank you for your message! We will get back to you soon.')
  }

  const categories = [
    'General Inquiry',
    'Partnership Opportunity',
    'Research Collaboration',
    'Media & Press',
    'IKIA Database Access',
    'Program Information',
    'Technical Support',
    'Other',
  ]

  return (
    <NPISection className="bg-gradient-to-br from-[#E5E1DC] via-[#CEC9BC] to-[#CABA9C]">
      <NPISectionHeader>
        <NPISectionTitle className="npi-text-pop-rust">{title}</NPISectionTitle>
        <NPISectionDescription className="text-[#46372A]">{description}</NPISectionDescription>
      </NPISectionHeader>

      <div className="grid lg:grid-cols-3 gap-8">
        {/* Contact Information */}
        <div className="lg:col-span-1 space-y-6">
          {contactInfo.map((info, index) => {
            const cardColors = [
              {
                bg: 'bg-gradient-to-br from-[#102820] to-[#4C6444]',
                icon: 'bg-[#8D8F78]',
                text: 'text-[#E5E1DC]',
                border: 'border-[#4C6444]',
              },
              {
                bg: 'bg-gradient-to-br from-[#6E3C19] to-[#8A6240]',
                icon: 'bg-[#A7795E]',
                text: 'text-[#E5E1DC]',
                border: 'border-[#8A6240]',
              },
              {
                bg: 'bg-gradient-to-br from-[#8D8F78] to-[#CABA9C]',
                icon: 'bg-[#4C6444]',
                text: 'text-[#2F2C29]',
                border: 'border-[#8D8F78]',
              },
            ]
            const colorScheme = cardColors[index % cardColors.length]

            return (
              <NPICard
                key={index}
                className={`hover:shadow-xl transition-all duration-300 ${colorScheme.bg} ${colorScheme.border} border-2 hover:scale-105`}
              >
                <NPICardHeader>
                  <div className="flex items-center gap-3 mb-3">
                    <div
                      className={`w-12 h-12 ${colorScheme.icon} flex items-center justify-center text-[#E5E1DC] shadow-lg`}
                    >
                      {info.icon}
                    </div>
                    <NPICardTitle className={`text-lg ${colorScheme.text} font-bold`}>
                      {info.title}
                    </NPICardTitle>
                  </div>
                </NPICardHeader>
                <NPICardContent>
                  <div className="space-y-1">
                    {info.details.map((detail, detailIndex) => (
                      <p key={detailIndex} className={`${colorScheme.text} font-npi opacity-90`}>
                        {detail}
                      </p>
                    ))}
                  </div>
                </NPICardContent>
              </NPICard>
            )
          })}

          {/* Social Media - Footer Style */}
          <NPICard>
            <NPICardHeader>
              <NPICardTitle className="text-lg">Follow Us</NPICardTitle>
            </NPICardHeader>
            <NPICardContent>
              <div className="flex gap-4">
                <motion.a
                  whileHover={{ scale: 1.15, rotate: 5 }}
                  whileTap={{ scale: 0.95 }}
                  href="https://facebook.com/npikenya"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="group transition-all duration-300"
                  aria-label="Follow us on Facebook"
                >
                  <div className="w-12 h-12 bg-gradient-to-br from-[#4C6444] to-[#102820] hover:from-[#8A6240] hover:to-[#A7795E] flex items-center justify-center transition-all duration-300 group-hover:shadow-xl group-hover:shadow-[#4C6444]/50 border-2 border-[#8D8F78] hover:border-[#A7795E]">
                    <svg
                      className="w-5 h-5 text-[#E5E1DC] group-hover:text-[#CEC9BC] transition-colors"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      viewBox="0 0 24 24"
                    >
                      <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z" />
                    </svg>
                  </div>
                </motion.a>
                <motion.a
                  whileHover={{ scale: 1.15, rotate: -5 }}
                  whileTap={{ scale: 0.95 }}
                  href="https://twitter.com/npikenya"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="group transition-all duration-300"
                  aria-label="Follow us on Twitter"
                >
                  <div className="w-12 h-12 bg-gradient-to-br from-[#8A6240] to-[#6E3C19] hover:from-[#A7795E] hover:to-[#CABA9C] flex items-center justify-center transition-all duration-300 group-hover:shadow-xl group-hover:shadow-[#8A6240]/50 border-2 border-[#A7795E] hover:border-[#CABA9C]">
                    <svg
                      className="w-5 h-5 text-[#E5E1DC] group-hover:text-[#CEC9BC] transition-colors"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      viewBox="0 0 24 24"
                    >
                      <path d="M23 3a10.9 10.9 0 0 1-3.14 1.53 4.48 4.48 0 0 0-7.86 3v1A10.66 10.66 0 0 1 3 4s-4 9 5 13a11.64 11.64 0 0 1-7 2c9 5 20 0 20-11.5a4.5 4.5 0 0 0-.08-.83A7.72 7.72 0 0 0 23 3z" />
                    </svg>
                  </div>
                </motion.a>
                <motion.a
                  whileHover={{ scale: 1.15, rotate: 5 }}
                  whileTap={{ scale: 0.95 }}
                  href="https://linkedin.com/company/npikenya"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="group transition-all duration-300"
                  aria-label="Follow us on LinkedIn"
                >
                  <div className="w-12 h-12 bg-gradient-to-br from-[#102820] to-[#4C6444] hover:from-[#6E3C19] hover:to-[#8A6240] flex items-center justify-center transition-all duration-300 group-hover:shadow-xl group-hover:shadow-[#102820]/50 border-2 border-[#4C6444] hover:border-[#8A6240]">
                    <svg
                      className="w-5 h-5 text-[#E5E1DC] group-hover:text-[#CEC9BC] transition-colors"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      viewBox="0 0 24 24"
                    >
                      <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z" />
                      <rect x="2" y="9" width="4" height="12" />
                      <circle cx="4" cy="4" r="2" />
                    </svg>
                  </div>
                </motion.a>
                <motion.a
                  whileHover={{ scale: 1.15, rotate: -5 }}
                  whileTap={{ scale: 0.95 }}
                  href="https://youtube.com/@npikenya"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="group transition-all duration-300"
                  aria-label="Follow us on YouTube"
                >
                  <div className="w-12 h-12 bg-gradient-to-br from-[#6E3C19] to-[#34170D] hover:from-[#A7795E] hover:to-[#8A6240] flex items-center justify-center transition-all duration-300 group-hover:shadow-xl group-hover:shadow-[#6E3C19]/50 border-2 border-[#8A6240] hover:border-[#A7795E]">
                    <svg
                      className="w-5 h-5 text-[#E5E1DC] group-hover:text-[#CEC9BC] transition-colors"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      viewBox="0 0 24 24"
                    >
                      <path d="M22.54 6.42a2.78 2.78 0 0 0-1.94-2C18.88 4 12 4 12 4s-6.88 0-8.6.46a2.78 2.78 0 0 0-1.94 2A29 29 0 0 0 1 11.75a29 29 0 0 0 .46 5.33A2.78 2.78 0 0 0 3.4 19c1.72.46 8.6.46 8.6.46s6.88 0 8.6-.46a2.78 2.78 0 0 0 1.94-2 29 29 0 0 0 .46-5.25 29 29 0 0 0-.46-5.33z" />
                      <polygon points="9.75,15.02 15.5,11.75 9.75,8.48" />
                    </svg>
                  </div>
                </motion.a>
              </div>
            </NPICardContent>
          </NPICard>
        </div>

        {/* Contact Form */}
        <div className="lg:col-span-2">
          <NPICard className="bg-gradient-to-br from-[#E5E1DC] to-[#CEC9BC] border-2 border-[#A7795E] shadow-xl">
            <NPICardHeader className="bg-gradient-to-r from-[#6E3C19] to-[#8A6240] -m-6 mb-6 p-6">
              <NPICardTitle className="text-2xl flex items-center gap-3 text-[#E5E1DC]">
                <MessageSquare className="w-6 h-6 text-[#CABA9C]" />
                Send us a Message
              </NPICardTitle>
            </NPICardHeader>
            <NPICardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <label
                      htmlFor="name"
                      className="block text-sm font-bold mb-2 font-npi text-[#6E3C19]"
                    >
                      Full Name *
                    </label>
                    <div className="relative">
                      <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#8A6240] w-5 h-5" />
                      <input
                        type="text"
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        required
                        className="w-full pl-10 pr-4 py-3 border-2 border-[#8D8F78] focus:border-[#6E3C19] bg-[#E5E1DC] focus:outline-none focus:ring-2 focus:ring-[#A7795E] font-npi transition-all duration-300"
                        placeholder="Enter your full name"
                      />
                    </div>
                  </div>

                  <div>
                    <label htmlFor="email" className="block text-sm font-medium mb-2 font-npi">
                      Email Address *
                    </label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5" />
                      <input
                        type="email"
                        id="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        required
                        className="w-full pl-10 pr-4 py-3 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent font-npi"
                        placeholder="Enter your email address"
                      />
                    </div>
                  </div>
                </div>

                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <label
                      htmlFor="organization"
                      className="block text-sm font-medium mb-2 font-npi"
                    >
                      Organization
                    </label>
                    <input
                      type="text"
                      id="organization"
                      name="organization"
                      value={formData.organization}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent font-npi"
                      placeholder="Your organization (optional)"
                    />
                  </div>

                  <div>
                    <label htmlFor="category" className="block text-sm font-medium mb-2 font-npi">
                      Category *
                    </label>
                    <select
                      id="category"
                      name="category"
                      value={formData.category}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent font-npi"
                    >
                      <option value="">Select a category</option>
                      {categories.map((category) => (
                        <option key={category} value={category}>
                          {category}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                <div>
                  <label htmlFor="subject" className="block text-sm font-medium mb-2 font-npi">
                    Subject *
                  </label>
                  <input
                    type="text"
                    id="subject"
                    name="subject"
                    value={formData.subject}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent font-npi"
                    placeholder="Brief subject of your message"
                  />
                </div>

                <div>
                  <label htmlFor="message" className="block text-sm font-medium mb-2 font-npi">
                    Message *
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    value={formData.message}
                    onChange={handleInputChange}
                    required
                    rows={6}
                    className="w-full px-4 py-3 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent font-npi resize-vertical"
                    placeholder="Please provide details about your inquiry..."
                  />
                </div>

                <div className="flex items-center gap-4">
                  <NPIButton
                    type="submit"
                    variant="primary"
                    size="lg"
                    disabled={isSubmitting}
                    className="flex-1 sm:flex-none bg-gradient-to-r from-[#6E3C19] to-[#8A6240] hover:from-[#8A6240] hover:to-[#A7795E] border-2 border-[#4C6444] hover:border-[#102820] shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
                  >
                    {isSubmitting ? (
                      <>
                        <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                        Sending...
                      </>
                    ) : (
                      <>
                        <Send className="w-5 h-5 mr-2" />
                        Send Message
                      </>
                    )}
                  </NPIButton>

                  <p className="text-sm text-[#46372A] font-npi font-medium">
                    We typically respond within 24-48 hours
                  </p>
                </div>
              </form>
            </NPICardContent>
          </NPICard>
        </div>
      </div>

      {/* Map Section */}
      <div className="mt-12">
        <NPICard className="bg-gradient-to-br from-[#102820] to-[#4C6444] border-2 border-[#8D8F78] shadow-xl">
          <NPICardHeader className="bg-gradient-to-r from-[#8D8F78] to-[#CABA9C] -m-6 mb-6 p-6">
            <NPICardTitle className="text-xl text-[#2F2C29] font-bold">Find Us</NPICardTitle>
          </NPICardHeader>
          <NPICardContent>
            <div className="h-64 bg-gradient-to-br from-[#CABA9C] to-[#A7795E] border-2 border-[#8A6240] flex items-center justify-center shadow-inner">
              <div className="text-center">
                <MapPin className="w-12 h-12 text-[#6E3C19] mx-auto mb-4 drop-shadow-lg" />
                <p className="text-[#2F2C29] font-npi font-semibold">
                  Interactive map will be integrated here
                </p>
                <p className="text-sm text-[#46372A] font-npi font-medium">
                  National Museums of Kenya, Museum Hill Road, Nairobi
                </p>
              </div>
            </div>
          </NPICardContent>
        </NPICard>
      </div>
    </NPISection>
  )
}
